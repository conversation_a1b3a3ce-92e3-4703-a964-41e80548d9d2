{"identification": "w5soar", "is_public": true, "name": "防火墙IP封禁器", "version": "1.0", "description": "防火墙IP封禁工具，用于自动化将指定IP地址添加到防火墙封禁列表，支持状态查询", "type": "网络安全", "action": [{"name": "封禁IP地址", "func": "block_ip_address"}, {"name": "查询封禁状态", "func": "query_block_status"}], "args": {"block_ip_address": [{"key": "ip_address", "type": "text", "default": "*******", "required": true, "description": "要封禁的IP地址"}], "query_block_status": [{"key": "ip_address", "type": "text", "default": "*******", "required": false, "description": "要查询状态的IP地址，留空则查询所有已封禁IP"}]}}