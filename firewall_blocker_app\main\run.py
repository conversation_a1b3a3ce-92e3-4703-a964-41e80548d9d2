# -*- coding: utf-8 -*-
"""
防火墙IP封禁器 SOAR 组件
模仿 access_query_app 的格式，提供防火墙IP封禁功能
"""

import asyncio
import logging
import re
import requests
from typing import List, Dict, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 禁用SSL警告
requests.packages.urllib3.disable_warnings(requests.packages.urllib3.exceptions.InsecureRequestWarning)


class FirewallConfig:
    """防火墙配置类"""
    
    def __init__(self):
        self.target_ip = "*************"
        self.username = "wabsuper"
        self.password = "whgD@955989"
        self.host_name_encoded = "01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7"
        self.timeout = 10
        self.max_retries = 3


class FirewallBlocker:
    """防火墙封禁管理类"""
    
    def __init__(self, config: FirewallConfig):
        self.config = config
        self.base_url = f"https://{config.target_ip}/cgi/maincgi.cgi"
        self.session = None
        self.is_logged_in = False
        
    def _create_session(self):
        """创建HTTP会话"""
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        })
        
    def _login(self) -> bool:
        """登录防火墙系统"""
        if not self.session:
            self._create_session()
            
        logger.info("正在尝试登录防火墙系统...")
        
        login_payload = {
            "username": self.config.username,
            "passwd": self.config.password,
            "loginSubmitIpt": ""
        }
        
        try:
            login_resp = self.session.post(
                f"{self.base_url}?Url=Index",
                data=login_payload,
                verify=False,
                timeout=self.config.timeout
            )
            login_resp.raise_for_status()
            
            if "username" in login_resp.text and "passwd" in login_resp.text:
                logger.error("登录失败！请检查用户名和密码")
                return False
            
            logger.info("✅ 登录成功！")
            self.is_logged_in = True
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"登录请求失败: {e}")
            return False
    
    def _get_blocked_ips(self) -> Optional[List[str]]:
        """获取当前已封禁的IP列表"""
        if not self.is_logged_in:
            if not self._login():
                return None
                
        logger.info("正在查询已封禁的IP列表...")
        edit_page_url = f"{self.base_url}?Url=HostObj&Act=Edit&Name={self.config.host_name_encoded}"
        
        try:
            get_ip_resp = self.session.get(edit_page_url, verify=False, timeout=self.config.timeout)
            get_ip_resp.raise_for_status()
            
            match = re.search(r"var ip_str = '(.*?)';", get_ip_resp.text, re.S)
            
            if not match:
                logger.error("无法在页面上找到已封禁的IP列表")
                return None
            
            ip_string = match.group(1).strip()
            old_ips = ip_string.split() if ip_string else []
            
            logger.info(f"✅ 查询成功！当前已封禁 {len(old_ips)} 个IP")
            return old_ips
            
        except requests.exceptions.RequestException as e:
            logger.error(f"查询IP列表失败: {e}")
            return None
    
    def _add_ip_to_list(self, ip_to_add: str, current_ips: List[str]) -> bool:
        """将IP添加到封禁列表"""
        logger.info(f"正在添加新IP '{ip_to_add}' 到封禁列表...")
        
        edit_page_url = f"{self.base_url}?Url=HostObj&Act=Edit&Name={self.config.host_name_encoded}"
        
        # 构建POST请求体
        payload_parts = [
            f"def_host_name={self.config.host_name_encoded}",
            "def_host_mac=00%3A00%3A00%3A00%3A00%3A00"
        ]
        
        all_ips = current_ips + [ip_to_add]
        for ip in all_ips:
            payload_parts.append(f"def_host_ipad={ip}")
            
        payload_parts.extend([
            f"host_ipad_input={ip_to_add}",
            f"name_hidden={self.config.host_name_encoded}",
            "def_host_frompage=",
            "def_host_from=",
            "def_host_edt_but=+%C8%B7%B6%A8+"
        ])
        
        final_payload_string = "&".join(payload_parts)
        
        try:
            update_headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Origin": f"https://{self.config.target_ip}",
                "Referer": edit_page_url
            }
            
            update_resp = self.session.post(
                edit_page_url, 
                data=final_payload_string.encode('utf-8'),
                headers=update_headers,
                verify=False, 
                timeout=self.config.timeout
            )
            update_resp.raise_for_status()
            
            if update_resp.status_code == 200:
                logger.info(f"✅ 提交成功！IP '{ip_to_add}' 已被添加")
                return True
            else:
                logger.error(f"提交失败，服务器返回状态码: {update_resp.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"提交更新请求失败: {e}")
            return False
    
    def block_single_ip(self, ip_address: str) -> Dict[str, Any]:
        """封禁单个IP地址"""
        logger.info(f"开始封禁IP地址: {ip_address}")
        
        # 验证IP格式
        if not self._validate_ip(ip_address):
            return {
                "ip_address": ip_address,
                "action": "block",
                "success": False,
                "message": "IP地址格式不正确",
                "already_blocked": False,
                "total_blocked_ips": 0
            }
        
        # 获取当前封禁列表
        current_ips = self._get_blocked_ips()
        if current_ips is None:
            return {
                "ip_address": ip_address,
                "action": "block",
                "success": False,
                "message": "无法获取当前封禁列表",
                "already_blocked": False,
                "total_blocked_ips": 0
            }
        
        # 检查IP是否已被封禁
        if ip_address in current_ips:
            logger.info(f"IP地址 '{ip_address}' 已存在于封禁列表中")
            return {
                "ip_address": ip_address,
                "action": "block",
                "success": True,
                "message": "IP地址已存在于封禁列表中，无需重复添加",
                "already_blocked": True,
                "total_blocked_ips": len(current_ips)
            }
        
        # 添加IP到封禁列表
        success = self._add_ip_to_list(ip_address, current_ips)
        
        return {
            "ip_address": ip_address,
            "action": "block",
            "success": success,
            "message": "IP地址已成功添加到封禁列表" if success else "添加IP到封禁列表失败",
            "already_blocked": False,
            "total_blocked_ips": len(current_ips) + (1 if success else 0)
        }
    
    def _validate_ip(self, ip_address: str) -> bool:
        """验证IP地址格式"""
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(pattern, ip_address):
            return False
        
        parts = ip_address.split('.')
        for part in parts:
            if int(part) > 255:
                return False
        
        return True
    
    def close(self):
        """关闭会话"""
        if self.session:
            self.session.close()
            self.session = None
            self.is_logged_in = False


# SOAR接口函数

async def block_ip_address(ip_address: str) -> Dict[str, Any]:
    """
    封禁IP地址 - SOAR接口函数
    
    Args:
        ip_address: 要封禁的IP地址
        
    Returns:
        dict: 封禁结果
    """
    logger.info(f"[防火墙封禁器] 开始封禁IP地址: {ip_address}")
    
    try:
        config = FirewallConfig()
        blocker = FirewallBlocker(config)
        
        try:
            result = blocker.block_single_ip(ip_address)
            
            return {
                "status": 0,
                "result": result
            }
            
        finally:
            blocker.close()
            
    except Exception as e:
        logger.error(f"封禁IP地址出错: {str(e)}")
        return {
            "status": 1,
            "result": f"封禁IP地址出错: {str(e)}"
        }





async def query_block_status(ip_address: str = "") -> Dict[str, Any]:
    """
    查询封禁状态 - SOAR接口函数
    
    Args:
        ip_address: 要查询状态的IP地址，留空则查询所有已封禁IP
        
    Returns:
        dict: 查询结果
    """
    logger.info(f"[防火墙封禁器] 查询封禁状态: {ip_address if ip_address else '所有IP'}")
    
    try:
        config = FirewallConfig()
        blocker = FirewallBlocker(config)
        
        try:
            blocked_ips = blocker._get_blocked_ips()
            
            if blocked_ips is None:
                return {
                    "status": 1,
                    "result": "无法获取封禁列表"
                }
            
            if ip_address:
                # 查询特定IP
                is_blocked = ip_address in blocked_ips
                return {
                    "status": 0,
                    "result": {
                        "query_ip": ip_address,
                        "is_blocked": is_blocked,
                        "total_blocked_ips": len(blocked_ips),
                        "message": "IP地址在封禁列表中" if is_blocked else "IP地址不在封禁列表中"
                    }
                }
            else:
                # 查询所有封禁IP
                return {
                    "status": 0,
                    "result": {
                        "query_ip": "all",
                        "total_blocked_ips": len(blocked_ips),
                        "blocked_ips": blocked_ips,
                        "message": f"当前共有 {len(blocked_ips)} 个IP被封禁"
                    }
                }
                
        finally:
            blocker.close()
            
    except Exception as e:
        logger.error(f"查询封禁状态出错: {str(e)}")
        return {
            "status": 1,
            "result": f"查询封禁状态出错: {str(e)}"
        }


if __name__ == "__main__":
    """本地测试入口"""
    
    async def test_main():
        """测试主函数"""
        logger.info("开始本地测试防火墙IP封禁器")
        
        try:
            # 测试封禁单个IP
            test_ip = "*******"
            result = await block_ip_address(test_ip)
            
            print("\n" + "="*50)
            print("防火墙IP封禁器测试结果")
            print("="*50)
            print(f"封禁IP: {test_ip}")
            print(f"状态: {'成功' if result['status'] == 0 else '失败'}")
            
            if result['status'] == 0:
                info = result['result']
                print(f"操作结果: {info['message']}")
                print(f"是否已被封禁: {info['already_blocked']}")
                print(f"总封禁IP数: {info['total_blocked_ips']}")
            else:
                print(f"错误信息: {result['result']}")
            
            # 测试查询状态
            print("\n" + "-"*30)
            print("测试查询封禁状态")
            print("-"*30)
            
            status_result = await query_block_status()
            if status_result['status'] == 0:
                info = status_result['result']
                print(f"总封禁IP数: {info['total_blocked_ips']}")
                print(f"消息: {info['message']}")
            
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
    
    # 运行测试
    asyncio.run(test_main())
