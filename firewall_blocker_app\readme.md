# 防火墙IP封禁器 SOAR 组件

## 📋 组件概述

防火墙IP封禁器 SOAR 组件是一个专业的网络安全防护组件，专门用于自动化管理防火墙IP封禁列表。该组件能够快速、准确地将恶意IP地址添加到防火墙封禁列表中，为网络安全团队提供高效的威胁响应能力。

### 🎯 核心价值
- **自动化封禁**: 无需人工干预，自动完成IP地址封禁操作
- **状态查询**: 实时查询IP封禁状态，便于管理和审计
- **零配置部署**: 开箱即用，内置防火墙连接配置
- **智能重试**: 内置重试机制，确保操作成功率

## ✨ 功能特性

### 🔐 智能登录系统
- **自动登录**: 支持防火墙系统的用户名密码自动登录
- **会话管理**: 自动维护登录会话，确保操作连续性
- **SSL处理**: 自动处理自签名证书问题
- **超时控制**: 智能超时设置，适应不同网络环境

### 🚫 IP封禁管理
- **单IP封禁**: 快速封禁单个恶意IP地址
- **重复检测**: 自动检测IP是否已在封禁列表中
- **状态查询**: 查询指定IP或所有已封禁IP的状态

### 📊 数据处理能力
- **格式化输出**: 标准化的 JSON 数据格式
- **操作日志**: 详细的操作日志记录
- **错误处理**: 完善的错误处理和提示机制

## 🚀 支持的操作

### 封禁IP地址 (block_ip_address)

将指定的IP地址添加到防火墙封禁列表中。

#### 📥 输入参数
- **ip_address** (string, 必需): 要封禁的IP地址

#### ⚙️ 内置配置
- **防火墙地址**: `https://*************`
- **登录凭据**: 使用内置的用户名和密码
- **封禁列表**: 自动管理网安封禁内网地址列表

#### 📤 返回结果
```json
{
  "status": 0,
  "result": {
    "ip_address": "*******",
    "action": "block",
    "success": true,
    "message": "IP地址已成功添加到封禁列表",
    "already_blocked": false,
    "total_blocked_ips": 156
  }
}
```



### 查询封禁状态 (query_block_status)

查询指定IP地址或所有已封禁IP的状态信息。

#### 📥 输入参数
- **ip_address** (string, 可选): 要查询状态的IP地址，留空则查询所有已封禁IP

#### 📤 返回结果
```json
{
  "status": 0,
  "result": {
    "query_ip": "*******",
    "is_blocked": true,
    "total_blocked_ips": 156,
    "blocked_ips": ["*******", "********", "..."],
    "message": "IP地址在封禁列表中"
  }
}
```

## 🔧 技术架构

### 核心组件
组件采用模块化设计，基于现有的防火墙管理功能：

- ✅ **FirewallBlocker**: 防火墙封禁管理类，处理所有封禁操作
- ✅ **SessionManager**: 会话管理器，处理登录和会话维护
- ✅ **IPValidator**: IP地址验证器，确保输入格式正确
- ✅ **自动重试机制**: 内置智能重试逻辑，提高操作成功率
- ✅ **SSL证书处理**: 自动处理HTTPS自签名证书问题

### 依赖要求

#### 必需依赖
```python
requests>=2.25.0      # HTTP请求库
urllib3>=1.26.0       # HTTP客户端库
re                     # 正则表达式库（内置）
```

## 📖 使用指南

### 在 SOAR 工作流中使用

#### 步骤1: 添加组件
1. 在 SOAR 平台中打开工作流编辑器
2. 从组件库中拖拽"防火墙IP封禁器"组件到画布
3. 双击组件进行配置

#### 步骤2: 配置操作
1. 选择所需操作：
   - **封禁IP地址**: 封禁单个IP
   - **查询封禁状态**: 查询封禁状态
2. 配置输入参数
3. 可选：配置后续处理节点来处理返回结果

#### 步骤3: 执行工作流
1. 保存工作流配置
2. 点击"运行"按钮执行工作流
3. 查看执行结果和封禁状态

### 本地测试使用

```python
import asyncio
from firewall_blocker_app.main.run import block_ip_address, query_block_status

async def test_blocker():
    # 封禁单个IP
    result = await block_ip_address("*******")
    print(f"封禁结果: {result}")

    # 查询状态
    status_result = await query_block_status("*******")
    print(f"状态查询结果: {status_result}")

# 运行测试
asyncio.run(test_blocker())
```

## 🔄 工作流程详解

### 阶段1: 系统初始化 (约2-5秒)
1. **模块导入**: 导入防火墙管理库和依赖
2. **配置加载**: 加载防火墙连接配置
3. **会话创建**: 创建HTTP会话对象

### 阶段2: 防火墙登录 (约3-8秒)
1. **连接建立**: 连接到防火墙管理界面
2. **身份验证**: 使用内置凭据进行登录
3. **会话验证**: 验证登录状态

### 阶段3: 封禁操作 (约5-15秒)
1. **列表查询**: 获取当前封禁列表
2. **IP验证**: 验证要封禁的IP地址格式
3. **重复检测**: 检查IP是否已在列表中
4. **执行封禁**: 添加新IP到封禁列表
5. **结果确认**: 确认操作成功

### 阶段4: 结果处理 (约1-2秒)
1. **数据格式化**: 将操作结果转换为标准格式
2. **状态更新**: 更新封禁状态信息
3. **结果封装**: 封装最终结果并返回

## ⚠️ 注意事项

### 网络要求
- **连通性**: 确保 SOAR 系统能够访问防火墙管理界面
- **端口**: 需要开放 HTTPS (443) 端口
- **防火墙**: 确保防火墙规则允许管理访问

### 系统要求
- **防火墙状态**: 确保防火墙系统正常运行
- **用户权限**: 确保登录用户具有管理封禁列表的权限
- **网络稳定**: 确保网络连接稳定，避免操作中断

### 安全考虑
- **操作审计**: 所有封禁操作都会记录日志
- **权限控制**: 仅授权用户可执行封禁操作
- **误封防护**: 建议在封禁前进行IP地址验证

## 📊 返回数据字段说明

### 封禁操作字段说明
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `ip_address` | string | 操作的IP地址 | "*******" |
| `action` | string | 执行的操作 | "block" |
| `success` | boolean | 操作是否成功 | true |
| `message` | string | 操作结果消息 | "IP地址已成功添加到封禁列表" |
| `already_blocked` | boolean | IP是否已被封禁 | false |
| `total_blocked_ips` | integer | 总封禁IP数量 | 156 |

## 🔍 故障排除

### 常见问题
1. **登录失败**: 检查用户名密码配置
2. **网络超时**: 检查网络连接和防火墙设置
3. **权限不足**: 确认用户具有管理权限
4. **IP格式错误**: 确保IP地址格式正确

### 日志查看
组件会生成详细的操作日志，可通过 SOAR 平台查看执行详情和错误信息。

